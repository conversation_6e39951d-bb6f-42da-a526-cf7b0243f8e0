{"/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/a4c6aac8b18c180d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/a4c6aac8b18c180d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/TransportSecurity": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "TransportSecurity"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/91baa182c7a11977_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/91baa182c7a11977_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/91baa182c7a11977_1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/91baa182c7a11977_1"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4d89adc913736ca2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4d89adc913736ca2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/22ba9f02cfebaa9f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/22ba9f02cfebaa9f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a90e3fb8d904d5dc_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a90e3fb8d904d5dc_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cookies-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cookies-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/Network Persistent State": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Network Persistent State"}, "/Users/<USER>/Library/Application Support/Windsurf/Preferences": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Preferences"}, "/Users/<USER>/Library/Application Support/Windsurf/REPORT.md": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "REPORT.md"}, "/Users/<USER>/Library/Application Support/Windsurf/SharedStorage": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "SharedStorage"}, "/Users/<USER>/Library/Application Support/Windsurf/Trust Tokens-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Trust Tokens-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/code.lock": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "code.lock"}, "/Users/<USER>/Library/Application Support/Windsurf/languagepacks.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "languagepacks.json"}, "/Users/<USER>/Library/Application Support/Windsurf/machineid": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "machineid"}, "/Users/<USER>/Library/Application Support/Windsurf/reset_windsurf_identifiers.sh": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "reset_windsurf_identifiers.sh"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/output_20250613T230041/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/output_20250613T230041/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/output_logging_20250613T230042/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/output_logging_20250613T230042/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/output_logging_20250613T230042/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/output_logging_20250613T230042/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/output_20250613T225616/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/output_20250613T225616/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.json-language-features/JSON Language Server.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/output_logging_20250613T225616/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/output_logging_20250613T225616/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/QuotaManager-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/QuotaManager-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/User/keybindings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/keybindings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json.backup.20250613_225051": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json.backup.20250613_225051"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json.backup.20250613_225402": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json.backup.20250613_225402"}, "/Users/<USER>/Library/Application Support/Windsurf/User/settings.json.backup.20250613_225551": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/settings.json.backup.20250613_225551"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/workspace.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/workspace.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/Augment-Memories": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/Augment-Memories"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/25dd58e4-b424-4c48-9641-042a10254e92": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/25dd58e4-b424-4c48-9641-042a10254e92"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/387b9221-ef76-4360-97ea-9642fccc0a22": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/387b9221-ef76-4360-97ea-9642fccc0a22"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/489af9ee-a70b-4184-810d-9baadcbd5fd7": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/489af9ee-a70b-4184-810d-9baadcbd5fd7"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/storage.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/storage.json"}, "/Users/<USER>/Library/Application Support/Windsurf/Shared Dictionary/db-journal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Shared Dictionary/db-journal"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/CURRENT": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/CURRENT"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOCK": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOCK"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOG": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOG"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/LOG.old": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/LOG.old"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/output_20250614T002904/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/output_20250614T002904/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/output_logging_20250614T002904/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/output_logging_20250614T002904/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/output_logging_20250614T002904/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/output_logging_20250614T002904/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749826847458.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749826847458.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/mtime-cache.json"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedProfilesData/__default__profile__/extensions.user.cache": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedProfilesData/__default__profile__/extensions.user.cache"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/ScriptCache/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/ScriptCache/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/js/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedData/c1afeb8ae2b17dbdda415f9aa5dec23422c1fe47/chrome/wasm/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/index-dir/the-real-index": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/index-dir/the-real-index"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/editSessions.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/editSessions.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/main.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/main.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/network-shared.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/network-shared.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/ptyhost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/ptyhost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/remoteTunnelService.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/remoteTunnelService.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/sharedprocess.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/sharedprocess.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/telemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/telemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/terminal.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/terminal.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/userDataSync.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/userDataSync.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/network.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/network.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/notebook.rendering.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/notebook.rendering.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/renderer.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/renderer.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/views.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/views.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/output_20250614T074655/tasks.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/output_20250614T074655/tasks.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/extHostTelemetry.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/extHostTelemetry.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/exthost.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/exthost.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/vscode.github/GitHub.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/vscode.github/GitHub.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/vscode.git/Git.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/vscode.git/Git.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/output_logging_20250614T074656/1-Remote - Dev Containers (Windsurf).log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/output_logging_20250614T074656/1-Remote - Dev Containers (Windsurf).log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/output_logging_20250614T074656/2-.NET Install Tool.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/output_logging_20250614T074656/2-.NET Install Tool.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/codeium.windsurf/Windsurf.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/codeium.windsurf/Windsurf.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/Augment.vscode-augment/Augment.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/Augment.vscode-augment/Augment.log"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749832152252.txt": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/DotNetAcquisition-ms-dotnettools.vscode-dotnet-runtime-1749832152252.txt"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000038.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000038.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3481cc13e215ffd2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3481cc13e215ffd2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/SharedStorage-wal": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "SharedStorage-wal"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/72f7112d083f0dee_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/72f7112d083f0dee_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000039.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000039.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000040.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000040.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f7dad7deee8c8f7d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f7dad7deee8c8f7d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3149e4769993e281_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3149e4769993e281_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000041.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000041.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000042.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000042.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f7dbf2ffd2184d14_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f7dbf2ffd2184d14_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ac40e91be8dd6e38_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ac40e91be8dd6e38_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000043.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000043.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000045.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000045.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1d0260b4b01bad00_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1d0260b4b01bad00_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ad1f28be1c969e99_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ad1f28be1c969e99_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000046.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000046.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000047.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000047.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9a59e90454e9c708_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9a59e90454e9c708_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/bdfed1d573e5711e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/bdfed1d573e5711e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000048.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000048.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000049.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000049.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a1af4c22e2675098_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a1af4c22e2675098_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/63e27d0f0c69839f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/63e27d0f0c69839f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000050.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000050.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000051.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000051.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3659d367f384af9b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3659d367f384af9b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/aa5549a8342508ed_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/aa5549a8342508ed_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000052.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000052.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000054.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000054.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9ea70d06c4106fcf_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9ea70d06c4106fcf_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7a21949f78a9aac7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7a21949f78a9aac7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000055.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000055.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000056.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000056.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2107cc3d6502d201_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2107cc3d6502d201_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f4f4fe0cd05bfea7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f4f4fe0cd05bfea7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000057.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000057.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000058.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000058.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d75d4dc96def1d1c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d75d4dc96def1d1c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9ec6965efc9b1606_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9ec6965efc9b1606_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000059.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000059.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000060.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000060.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/cb52ace8e6bd52e9_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/cb52ace8e6bd52e9_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d0de2e3fb4a5bdb0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d0de2e3fb4a5bdb0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000061.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000061.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000063.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000063.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/39df776b3f13524a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/39df776b3f13524a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/cee6a450d2d90326_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/cee6a450d2d90326_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000064.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000064.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000065.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000065.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2ee99bfdd664d76c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2ee99bfdd664d76c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3218ef3ecac63bff_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3218ef3ecac63bff_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000066.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000066.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000067.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000067.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6be3d6ae6e38c2bb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6be3d6ae6e38c2bb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d86517e97d5afb32_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d86517e97d5afb32_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000068.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000068.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000069.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000069.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1da97ab4605e9f6b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1da97ab4605e9f6b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3a768d4ed79c1406_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3a768d4ed79c1406_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000070.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000070.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000072.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000072.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/df5d6f001d1a17ee_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/df5d6f001d1a17ee_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1d6865f4e7aa5978_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1d6865f4e7aa5978_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000073.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000073.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000074.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000074.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/fd88aba0da6459f2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/fd88aba0da6459f2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9d5e6ac9b2ab1a9c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9d5e6ac9b2ab1a9c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000075.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000075.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000076.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000076.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/38244dde21f4451b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/38244dde21f4451b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7595612a25ca6f59_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7595612a25ca6f59_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000077.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000077.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000078.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000078.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/fa74e8fa1b70ec04_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/fa74e8fa1b70ec04_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/dff6c42a671637c8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/dff6c42a671637c8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000079.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000079.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000081.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000081.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/879fdb5c32fe9413_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/879fdb5c32fe9413_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e1af54c63a6d6c38_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e1af54c63a6d6c38_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000082.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000082.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000083.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000083.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d714624dbb616b8d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d714624dbb616b8d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/68d31696b7c471b3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/68d31696b7c471b3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000084.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000084.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000085.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000085.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/89aef29384f00e5e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/89aef29384f00e5e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a15a1688829e0b72_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a15a1688829e0b72_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000086.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000086.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000087.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000087.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4a7ad2f35ca9ae26_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4a7ad2f35ca9ae26_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/61799e8ad071d3f0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/61799e8ad071d3f0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000088.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000088.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000090.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000090.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/46929ec54186759a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/46929ec54186759a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d141cd22c7f08270_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d141cd22c7f08270_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000091.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000091.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000092.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000092.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7fe272bad6f0f5f3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7fe272bad6f0f5f3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4582487e83b4f3a4_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4582487e83b4f3a4_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000093.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000093.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000094.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000094.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3bd1de697b42425c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3bd1de697b42425c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/eb5f9d5a8f62f350_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/eb5f9d5a8f62f350_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000095.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000095.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000096.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000096.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f8f23be1d8900ad6_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f8f23be1d8900ad6_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3411d4ec6ae2518d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3411d4ec6ae2518d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000097.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000097.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000099.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000099.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6c5ef97a9f037874_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6c5ef97a9f037874_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/47a90a84067e50fb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/47a90a84067e50fb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000100.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000100.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000101.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000101.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d176b6af2e97209c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d176b6af2e97209c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/667a6ead8df85f5a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/667a6ead8df85f5a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000102.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000102.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000103.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000103.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9ea9ace88596567b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9ea9ace88596567b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9f50bddbd978eddd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9f50bddbd978eddd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/eb7a734da8a37aa2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/eb7a734da8a37aa2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000104.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000104.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000105.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000105.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/df6fcd0437a1f82c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/df6fcd0437a1f82c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1a047c63d436fbc7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1a047c63d436fbc7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000106.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000106.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000108.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000108.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a45665e025baeaf7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a45665e025baeaf7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/25cb86e9ed349358_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/25cb86e9ed349358_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000109.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000109.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000110.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000110.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e5093158811c76d4_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e5093158811c76d4_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/10b425914808b0fd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/10b425914808b0fd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000111.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000111.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000112.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000112.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1885287a7c9c934a_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1885287a7c9c934a_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/65d9af677bb7dfba_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/65d9af677bb7dfba_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000113.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000113.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000114.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000114.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/148e517948b45d20_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/148e517948b45d20_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/66a491ea53586782_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/66a491ea53586782_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000115.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000115.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000117.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000117.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d816c2a08bf7c07d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d816c2a08bf7c07d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c9a56df6538e20d2_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c9a56df6538e20d2_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000118.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000118.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000119.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000119.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/8cb93ccd87858690_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/8cb93ccd87858690_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2b751e3d7cdaddbb_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2b751e3d7cdaddbb_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000120.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000120.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000121.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000121.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/65f83456c0b85a8c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/65f83456c0b85a8c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/3b6a9cf5e47c4630_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/3b6a9cf5e47c4630_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000122.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000122.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000123.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000123.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/add1c8f8870e6fd3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/add1c8f8870e6fd3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f033ac2a754e11dc_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f033ac2a754e11dc_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000124.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000124.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000126.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000126.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/5618f40db2970022_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/5618f40db2970022_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/0a5f1a3d0024768d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/0a5f1a3d0024768d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000127.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000127.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000128.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000128.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1768bc79de2c5780_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1768bc79de2c5780_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e9749e9530d4193f_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e9749e9530d4193f_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000129.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000129.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000130.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000130.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b4aad5b8336281ed_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b4aad5b8336281ed_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/87f288ac6520d59d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/87f288ac6520d59d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000131.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000131.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000132.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000132.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9056de3ec1d2d468_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9056de3ec1d2d468_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f5796e72e2cce43e_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f5796e72e2cce43e_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000133.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000133.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000135.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000135.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/de1fec7a6e175d70_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/de1fec7a6e175d70_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/dc03a19f280a8946_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/dc03a19f280a8946_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000136.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000136.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000137.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000137.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c84d8f7ec435c939_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c84d8f7ec435c939_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b7328d07b272a48d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b7328d07b272a48d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000138.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000138.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000139.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000139.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d92e50f3c6146d73_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d92e50f3c6146d73_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9c0d1c026a37e0d0_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9c0d1c026a37e0d0_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000140.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000140.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000141.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000141.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/95c6895554e51aac_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/95c6895554e51aac_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6ce3c3744aa1d153_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6ce3c3744aa1d153_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000142.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000142.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000144.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000144.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a7a6b741258ab8ac_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a7a6b741258ab8ac_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/895ef8471aa1bc9c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/895ef8471aa1bc9c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000145.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000145.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000146.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000146.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4835889149fb804d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4835889149fb804d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/7537b24013e1d860_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/7537b24013e1d860_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000147.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000147.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000148.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000148.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6a322e352e7b81ec_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6a322e352e7b81ec_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4a0933e0e0971a06_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4a0933e0e0971a06_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000149.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000149.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000150.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000150.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e32b73e5913725ea_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e32b73e5913725ea_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/1f19e8e5ce2205b8_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/1f19e8e5ce2205b8_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000151.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000151.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000153.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000153.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/d460bf91cd8fa070_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/d460bf91cd8fa070_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/b444f5199c02b9fa_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/b444f5199c02b9fa_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000154.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000154.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000155.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000155.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/8c0f614cd661418d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/8c0f614cd661418d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/091189931297e767_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/091189931297e767_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000156.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000156.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000157.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000157.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/ae6dd3c9cd351556_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/ae6dd3c9cd351556_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/359f086fed4dcd48_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/359f086fed4dcd48_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000158.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000158.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000159.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000159.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/dde081bb84d2d6f7_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/dde081bb84d2d6f7_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/69d24fcdc4c138df_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/69d24fcdc4c138df_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/098978814ebcb67b_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/098978814ebcb67b_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000160.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000160.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000162.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000162.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c56926febb7dda4c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c56926febb7dda4c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/e2579b5808afc90c_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/e2579b5808afc90c_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000163.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000163.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000164.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000164.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a9efa1373eef6e76_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a9efa1373eef6e76_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/367e0f25d86e690d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/367e0f25d86e690d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000165.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000165.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000166.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000166.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/f0ef2539907dbe9d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/f0ef2539907dbe9d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/c3803b2aabdcdd11_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/c3803b2aabdcdd11_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000167.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000167.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000168.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000168.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/9772c86db7f43c10_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/9772c86db7f43c10_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/69844cdb143c94a5_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/69844cdb143c94a5_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000169.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000169.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000171.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000171.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/a65ec934fa5caf43_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/a65ec934fa5caf43_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/149b659e97c8e3d5_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/149b659e97c8e3d5_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000172.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000172.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000173.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000173.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/53d10518de533f8d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/53d10518de533f8d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/4b4f08ff42b54015_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/4b4f08ff42b54015_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000174.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000174.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000175.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000175.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/06b7c1600800d144_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/06b7c1600800d144_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/6c23e2e8ef9af7bd_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/6c23e2e8ef9af7bd_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000176.log": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000176.log"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/000177.ldb": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/000177.ldb"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/2ae5eaa6b83cb8b3_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/2ae5eaa6b83cb8b3_0"}, "/Users/<USER>/Library/Application Support/Windsurf/Cache/Cache_Data/34facd7d904a22df_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Cache/Cache_Data/34facd7d904a22df_0"}}
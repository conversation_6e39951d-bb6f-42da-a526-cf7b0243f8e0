{"/Users/<USER>/Library/Application Support/Windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": ""}, "/Users/<USER>/Library/Application Support/Windsurf/User/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/augment.vscode-augment/augment-global-state/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/augment.vscode-augment/augment-global-state/"}, "/Users/<USER>/Library/Application Support/Windsurf/Shared Dictionary/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Shared Dictionary/"}, "/Users/<USER>/Library/Application Support/Windsurf/Session Storage/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Session Storage/"}, "/Users/<USER>/Library/Application Support/Windsurf/Service Worker/Database/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Service Worker/Database/"}, "/Users/<USER>/Library/Application Support/Windsurf/Local Storage/leveldb/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "Local Storage/leveldb/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/output_20250613T230041/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/output_20250613T230041/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/output_logging_20250613T230042/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/output_logging_20250613T230042/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/output_20250613T225616/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/output_20250613T225616/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.json-language-features/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.json-language-features/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/output_logging_20250613T225616/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/output_logging_20250613T225616/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T225615/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T225615/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/tasks/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/task-storage/manifest/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/augment-global-state/"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/"}, "/Users/<USER>/Library/Application Support/Windsurf/User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/workspaceStorage/c59be2f483dcdc78296c3a4a55c688ec/Augment.vscode-augment/be22e0eb5631f7a836ee09792d06e1e780e3df707f2da3b4efcf0d75c8fae0b7/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/output_20250614T002904/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/output_20250614T002904/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/output_logging_20250614T002904/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/output_logging_20250614T002904/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250613T230040/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250613T230040/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}, "/Users/<USER>/Library/Application Support/Windsurf/CachedProfilesData/__default__profile__/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "CachedProfilesData/__default__profile__/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/output_20250614T074655/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/output_20250614T074655/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/vscode.github/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/vscode.github/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/vscode.git/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/vscode.git/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/output_logging_20250614T074656/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/output_logging_20250614T074656/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/codeium.windsurf/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/codeium.windsurf/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T074654/window1/exthost/Augment.vscode-augment/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T074654/window1/exthost/Augment.vscode-augment/"}, "/Users/<USER>/Library/Application Support/Windsurf/logs/20250614T002902/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "logs/20250614T002902/window1/exthost/ms-dotnettools.vscode-dotnet-runtime/"}}
{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": [], "broken_count": 3, "host": "cdnjs.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": [], "broken_count": 5, "host": "o4507463137361920.ingest.us.sentry.io", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396890684587953", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": [], "server": "https://windsurf-stable.codeium.com", "supports_spdy": true}, {"anonymization": [], "server": "https://marketplace.windsurf.com", "supports_spdy": true}, {"anonymization": [], "server": "https://cdnjs.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396884311863121", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394382594151637", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://unpkg.com", "supports_spdy": true}, {"anonymization": [], "server": "https://o4507463137361920.ingest.us.sentry.io", "supports_spdy": true}, {"anonymization": [], "server": "https://main.vscode-cdn.net", "supports_spdy": true}, {"anonymization": [], "server": "https://unleash.codeium.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G", "CAISABiAgICA+P////8B": "3G", "CAYSABiAgICA+P////8B": "Offline"}}}